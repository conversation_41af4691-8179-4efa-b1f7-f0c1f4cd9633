package com.collabhub.be.modules.chat.model;

/**
 * Enum representing different types of chat messages.
 * Used to distinguish between user messages and system-generated messages.
 */
public enum MessageType {
    
    /**
     * Regular message sent by a user.
     */
    USER,
    
    /**
     * System message indicating a member was added to the chat.
     * Format: "{actor} added {target} to the chat"
     */
    MEMBER_ADDED,
    
    /**
     * System message indicating a member was removed from the chat.
     * Format: "{actor} removed {target} from the chat"
     */
    MEMBER_REMOVED;
    
    /**
     * Checks if this message type is a system message.
     * 
     * @return true if this is a system message type, false otherwise
     */
    public boolean isSystemMessage() {
        return this != USER;
    }
    
    /**
     * Gets the display name for the message type.
     * 
     * @return human-readable name for the message type
     */
    public String getDisplayName() {
        return switch (this) {
            case USER -> "User Message";
            case MEMBER_ADDED -> "Member Added";
            case MEMBER_REMOVED -> "Member Removed";
        };
    }
}
