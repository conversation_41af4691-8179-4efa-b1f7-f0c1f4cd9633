package com.collabhub.be.modules.chat.service;

import com.collabhub.be.modules.chat.dto.ChatMessageResponse;
import com.collabhub.be.modules.chat.model.MessageType;
import com.collabhub.be.modules.chat.repository.ChatMessageRepositoryImpl;
import com.collabhub.be.modules.collaborationhub.repository.HubParticipantRepositoryImpl;
import jakarta.validation.constraints.NotNull;
import org.jooq.JSONB;
import org.jooq.generated.tables.pojos.ChatMessage;
import org.jooq.generated.tables.pojos.HubParticipant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service for creating and managing system messages in chat channels.
 * System messages are automatically generated for events like member additions/removals.
 */
@Service
public class SystemMessageService {

    private static final Logger logger = LoggerFactory.getLogger(SystemMessageService.class);

    private final ChatMessageRepositoryImpl messageRepository;
    private final HubParticipantRepositoryImpl participantRepository;
    private final ChatMessageService messageService;

    @Autowired
    public SystemMessageService(ChatMessageRepositoryImpl messageRepository,
                              HubParticipantRepositoryImpl participantRepository,
                              ChatMessageService messageService) {
        this.messageRepository = messageRepository;
        this.participantRepository = participantRepository;
        this.messageService = messageService;
    }

    /**
     * Creates a system message for member addition.
     *
     * @param channelId the channel ID
     * @param actorParticipantId the participant who performed the action
     * @param addedParticipantIds the participants who were added
     * @return the created system message response
     */
    @Transactional
    public ChatMessageResponse createMemberAddedMessage(@NotNull Long channelId,
                                                       @NotNull Long actorParticipantId,
                                                       @NotNull List<Long> addedParticipantIds) {
        logger.info("Creating MEMBER_ADDED system message for channel {} by participant {} for {} participants",
                   channelId, actorParticipantId, addedParticipantIds.size());

        String content = buildMemberAddedContent(actorParticipantId, addedParticipantIds);
        return createSystemMessage(channelId, actorParticipantId, MessageType.MEMBER_ADDED, content);
    }

    /**
     * Creates a system message for member removal.
     *
     * @param channelId the channel ID
     * @param actorParticipantId the participant who performed the action
     * @param removedParticipantIds the participants who were removed
     * @return the created system message response
     */
    @Transactional
    public ChatMessageResponse createMemberRemovedMessage(@NotNull Long channelId,
                                                         @NotNull Long actorParticipantId,
                                                         @NotNull List<Long> removedParticipantIds) {
        logger.info("Creating MEMBER_REMOVED system message for channel {} by participant {} for {} participants",
                   channelId, actorParticipantId, removedParticipantIds.size());

        String content = buildMemberRemovedContent(actorParticipantId, removedParticipantIds);
        return createSystemMessage(channelId, actorParticipantId, MessageType.MEMBER_REMOVED, content);
    }

    /**
     * Creates a system message with the specified type and content.
     */
    private ChatMessageResponse createSystemMessage(Long channelId, Long actorParticipantId,
                                                   MessageType messageType, String content) {
        // Create system message record (no mentions for system messages)
        JSONB emptyMentions = JSONB.valueOf("[]");
        ChatMessage message = messageRepository.createSystemMessage(channelId, actorParticipantId, content, messageType, emptyMentions);

        // Update channel activity
        messageService.updateChannelActivity(channelId);

        // Build response using existing service method
        HubParticipant actor = participantRepository.findById(actorParticipantId);
        return messageService.buildSystemMessageResponse(message, actor, messageType);
    }

    /**
     * Builds content for member added system message.
     */
    private String buildMemberAddedContent(Long actorParticipantId, List<Long> addedParticipantIds) {
        HubParticipant actor = participantRepository.findById(actorParticipantId);
        String actorName = getParticipantDisplayName(actor);

        if (addedParticipantIds.size() == 1) {
            HubParticipant addedParticipant = participantRepository.findById(addedParticipantIds.get(0));
            String addedName = getParticipantDisplayName(addedParticipant);
            return String.format("%s added %s to the chat", actorName, addedName);
        } else {
            return String.format("%s added %d participants to the chat", actorName, addedParticipantIds.size());
        }
    }

    /**
     * Builds content for member removed system message.
     */
    private String buildMemberRemovedContent(Long actorParticipantId, List<Long> removedParticipantIds) {
        HubParticipant actor = participantRepository.findById(actorParticipantId);
        String actorName = getParticipantDisplayName(actor);

        if (removedParticipantIds.size() == 1) {
            HubParticipant removedParticipant = participantRepository.findById(removedParticipantIds.get(0));
            String removedName = getParticipantDisplayName(removedParticipant);
            return String.format("%s removed %s from the chat", actorName, removedName);
        } else {
            return String.format("%s removed %d participants from the chat", actorName, removedParticipantIds.size());
        }
    }

    /**
     * Gets display name for a participant, with fallback to email.
     */
    private String getParticipantDisplayName(HubParticipant participant) {
        if (participant == null) {
            return "Unknown User";
        }
        return participant.getName() != null && !participant.getName().trim().isEmpty()
                ? participant.getName()
                : participant.getEmail();
    }
}
