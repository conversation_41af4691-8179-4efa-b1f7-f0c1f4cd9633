import { $api } from '@/lib/api/client';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useTranslations } from '@/lib/i18n/typed-translations';

/**
 * Custom hook for fetching chat channels for a collaboration hub.
 * Uses openapi-react-query for type-safe API calls with React Query.
 *
 * The backend automatically:
 * - Scopes channels to the current account (multi-tenancy)
 * - Filters channels based on user permissions
 * - Returns channels with participant counts and last messages
 * - Includes unread message counts
 *
 * @param hubId - Hub ID to fetch channels for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannels(
  hubId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats', {
    params: {
      path: { hubId },
      query: {
        page: 0,
        size: 50, // Most hubs won't have more than 50 channels
      },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId,
    // Cache data for 30 seconds (channels change less frequently)
    staleTime: options?.staleTime ?? 30 * 1000,
    // Enable automatic refetching on window focus for fresh unread counts
    refetchOnWindowFocus: true,
  });
}

/**
 * Custom hook for fetching a specific chat channel details.
 *
 * @param hubId - Hub ID
 * @param channelId - Channel ID to fetch details for
 * @param options - Query options including enabled and staleTime
 */
export function useChatChannel(
  hubId: number,
  channelId: number,
  options?: { enabled?: boolean; staleTime?: number }
) {
  return $api.useQuery('get', '/api/hubs/{hubId}/chats/{channelId}', {
    params: {
      path: { hubId, channelId },
    },
  }, {
    enabled: options?.enabled !== false && !!hubId && !!channelId,
    // Cache data for 1 minute
    staleTime: options?.staleTime ?? 1 * 60 * 1000,
  });
}

/**
 * Custom hook for creating a custom chat channel.
 */
export function useCreateChannel() {
  const queryClient = useQueryClient();
  const { t, keys } = useTranslations();

  return $api.useMutation('post', '/api/hubs/{hubId}/chats', {
    onSuccess: (_, variables) => {
      const hubId = variables.params.path.hubId;

      // Invalidate channels list to show the new channel
      queryClient.invalidateQueries({
        queryKey: [
          'get',
          '/api/hubs/{hubId}/chats',
          {
            params: {
              path: { hubId },
              query: {}
            }
          }
        ],
      });

      // Show success toast notification
      toast.success(t(keys.collaborationHubs.chat.channelCreated));
    },
    onError: (__error) => {
      // Error handling for channel creation
    },
  });
}

/**
 * Custom hook for deleting a custom chat channel.
 */
export function useDeleteChannel() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{hubId}/chats/{channelId}', {
    onSuccess: (_, variables) => {
      const hubId = variables.params.path.hubId;

      // Invalidate channels list to remove the deleted channel
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { params: { path: { hubId } } }],
      });
    },
    onError: (__error) => {
      // Error handling for channel deletion
    },
  });
}

/**
 * Custom hook for adding participants to a custom chat channel.
 */
export function useAddChannelParticipants() {
  const queryClient = useQueryClient();

  return $api.useMutation('put', '/api/hubs/{hubId}/chats/{channelId}/participants', {
    onSuccess: (_, variables) => {
      const { hubId, channelId } = variables.params.path;

      // Invalidate channel details to update participant count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { params: { path: { hubId, channelId } } }],
      });

      // Invalidate channels list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { params: { path: { hubId } } }],
      });

      // Invalidate chat messages to show system messages (match the useChatMessages key)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const k = query.queryKey as unknown[];
          return k?.[0] === 'get' && k?.[1] === '/api/chats/{channelId}/messages' && (k?.[2] as any)?.channelId === channelId;
        }
      });
    },
    onError: (__error) => {
      // Error handling for adding participants
    },
  });
}

/**
 * Custom hook for removing participants from a custom chat channel.
 */
export function useRemoveChannelParticipants() {
  const queryClient = useQueryClient();

  return $api.useMutation('delete', '/api/hubs/{hubId}/chats/{channelId}/participants', {
    onSuccess: (_, variables) => {
      const { hubId, channelId } = variables.params.path;

      // Invalidate channel details to update participant count
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats/{channelId}', { params: { path: { hubId, channelId } } }],
      });

      // Invalidate channels list to update participant counts
      queryClient.invalidateQueries({
        queryKey: ['get', '/api/hubs/{hubId}/chats', { params: { path: { hubId } } }],
      });

      // Invalidate chat messages to show system messages (match the useChatMessages key)
      queryClient.invalidateQueries({
        predicate: (query) => {
          const k = query.queryKey as unknown[];
          return k?.[0] === 'get' && k?.[1] === '/api/chats/{channelId}/messages' && (k?.[2] as any)?.channelId === channelId;
        }
      });
    },
    onError: (__error) => {
      // Error handling for removing participants
    },
  });
}
